import logging
import os
import sys
import tkinter as tk
from datetime import datetime
from tkinter import ttk, messagebox, filedialog
from typing import Optional, List, Dict
from zoneinfo import ZoneInfo

from config import gui_constants, constants, config_check
from models import single_instance_meta
from utils import server_utils, gui_utils
from utils.config_lock import MultiProcessConfigManager

logger: Optional[logging.Logger] = None


class ConfigSelectionGUI(metaclass=single_instance_meta.SingleInstanceMeta):
    def __init__(self):
        server_utils.logger_print(msg="initializing config selection gui", custom_logger=logger)
        
        # 配置管理器 参数值是gui界面生成配置文件的配置项的值,两者统一
        self.config_manager = MultiProcessConfigManager.get_singleton_instance(db_path=constants.CROSS_PROCESS_DATA_BASE_PATH, enable_sql_logging=gui_constants.ENABLE_SQL_LOGGING, zone=ZoneInfo(gui_constants.TIME_ZONE))
        if not self.config_manager.check_config_in_db():
            server_utils.logger_print(msg="no configs found, this is first run", custom_logger=logger)
            self.new_config_to_main_gui(first_run=True)
            return
        # 配置数据
        self.invalid_configs: List[Dict] = []
        self.valid_configs: List[Dict] = []
        self.occupied_configs: List[Dict] = []
        # 上一次导入配置的所在目录:默认是在gui配置目录下
        self.last_config_dir_on_import_config=server_utils.get_real_path_create_dir(path=gui_constants.SERVER_CONFIG_DIR,path_is_dir=True)

        # GUI元素
        self.root = tk.Tk()
        self.root.title("配置选择")
        self.root.geometry("700x600")  # 减少宽度，因为移除了路径列
        self.root.resizable(False, False)

        # 配置树视图
        self.config_tree: Optional[ttk.Treeview] = None
        self.status_label: Optional[ttk.Label] = None
        self.tree_tooltip: Optional[gui_utils.TreeViewRowToolTip] = None
        
        server_utils.logger_print(msg="creating config selection widgets", custom_logger=logger)
        self.create_widgets()
        
        # 应用通用GUI设置
        gui_utils.common_gui_do(self.root, gui_constants.ICON_PATH)
        gui_utils.bring_to_front(self.root)
        
        # 加载配置数据
        self.refresh_config_list()
        
        # 监听关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        server_utils.logger_print(msg="config selection gui initialization completed", custom_logger=logger)

    def create_widgets(self):
        """创建界面组件"""
        # 主内容区域
        content_frame = ttk.Frame(self.root)
        content_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # 标题
        title_label = ttk.Label(content_frame, text="配置选择", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 操作按钮区域
        btn_frame = ttk.Frame(content_frame)
        btn_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Button(btn_frame, text="新增配置", command=self.create_new_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="导入配置", command=self.import_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="清理无效", command=self.clear_invalid_configs).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="刷新列表", command=self.refresh_config_list).pack(side=tk.LEFT, padx=5)
        
        # 配置列表区域
        list_frame = ttk.LabelFrame(content_frame, text="配置列表")
        list_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # 创建树视图
        columns = ("status", "name", "path", "last_used", "info")
        self.config_tree = ttk.Treeview(list_frame, columns=columns, show="headings", selectmode="browse")
        
        # 设置列属性
        self.config_tree.column("status", width=80, anchor="center")
        self.config_tree.column("name", width=150, anchor="w")
        self.config_tree.column("path", width=300, anchor="w")
        self.config_tree.column("last_used", width=120, anchor="center")
        self.config_tree.column("info", width=200, anchor="w")
        
        # 设置表头
        self.config_tree.heading("status", text="状态")
        self.config_tree.heading("name", text="配置名称")
        self.config_tree.heading("path", text="配置路径")
        self.config_tree.heading("last_used", text="最后使用")
        self.config_tree.heading("info", text="附加信息")
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.config_tree.yview)  # noqa
        scrollbar.pack(side="right", fill="y")
        self.config_tree.configure(yscrollcommand=scrollbar.set)
        
        # 配置树视图
        self.config_tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 绑定双击事件
        gui_utils.on_tree_double_click(self.config_tree, self.select_config)
        
        # 状态标签
        self.status_label = ttk.Label(content_frame, text="正在加载配置...", font=("Arial", 10))
        self.status_label.pack(pady=(0, 10))
        
        # 底部按钮
        bottom_frame = ttk.Frame(self.root)
        bottom_frame.pack(side=tk.BOTTOM, fill='x', padx=20, pady=20)
        
        ttk.Button(bottom_frame, text="选择配置", command=self.select_config).pack(side=tk.RIGHT, padx=10)

    def refresh_config_list(self):
        """刷新配置列表"""
        server_utils.logger_print(msg="refreshing config list", custom_logger=logger)
        
        try:
            # 获取配置状态
            self.invalid_configs, self.valid_configs, self.occupied_configs = self.config_manager.refresh_config_status()
            
            # 清空现有数据
            self.config_tree.delete(*self.config_tree.get_children())
            # 添加配置到树视图
            self._add_configs_to_tree()
            
            # 更新状态标签
            self._update_status_label()
            
            server_utils.logger_print(msg="config list refreshed successfully", custom_logger=logger)
        except Exception as e:
            server_utils.logger_print(msg="error refreshing config list!", custom_logger=logger, use_exception=True, exception=e, print_error=True)
            messagebox.showerror("错误", f"刷新配置列表失败：{str(e)}")

    def new_config_to_main_gui(self,first_run:bool):
        msg= "handling first run scenario" if first_run else "creating new config"
        msg+="  to main gui"
        error_msg = "error in first run handling!" if first_run else "error creating new config!"
        error_zh_msg = "首次运行初始化失败！" if first_run else "创建新配置失败！"
        server_utils.logger_print(msg=msg, custom_logger=logger)

        try:
            config_path = config_check.create_new_config_file()
            server_utils.logger_print(msg=f"created new config file: {config_path}", custom_logger=logger)

            # 注册配置
            self.config_manager.register_config(config_path)
            server_utils.logger_print(msg=f"registered config: {config_path}", custom_logger=logger)

            # 直接进入主界面
            self.launch_main_gui(config_path)

        except Exception as e:
            server_utils.logger_print(msg=error_msg, custom_logger=logger, use_exception=True, exception=e, print_error=True)
            messagebox.showerror("错误", f"{error_zh_msg}：{str(e)}")
    def _add_configs_to_tree(self):
        """将配置添加到树视图"""
        # 添加可用配置
        for config in self.valid_configs:
            self.config_tree.insert("", "end",iid=config['id'], values=(
                "🟢 可用",
                config['name'],
                config['file_path'],
                ConfigSelectionGUI._format_time(config['last_used']),
                ""
            ), tags=("valid",))
        
        # 添加占用配置
        for config in self.occupied_configs:
            process_info = f"PID: {config['process_id']}"
            self.config_tree.insert("", "end",iid=config['id'], values=(
                "🔴 占用",
                config['name'],
                config['file_path'],
                ConfigSelectionGUI._format_time(config['last_used']),
                process_info
            ), tags=("occupied",))
        
        # 添加无效配置
        for config in self.invalid_configs:
            fail_reason = config['fail_reason']
            self.config_tree.insert("", "end",iid=config['id'], values=(
                "❌ 无效",
                config['name'],
                config['file_path'],
                ConfigSelectionGUI._format_time(config['last_used']),
                f"错误: {fail_reason}"
            ), tags=("invalid",))
    @staticmethod
    def _format_time(time_str: str) -> str:
        """
        格式化时间显示:
        由于`time_str`是从数据库中获取到的,其值已经经过时区转换,所以这里不需要再转换
        """
        if not time_str:
            return "未知"
        past = datetime.strptime(time_str, gui_constants.DATETIME_FORMAT)
        now = datetime.now()
        delta = now - past

        # 1 分钟内：秒
        seconds = int(delta.total_seconds())
        if seconds < 60:
            return f"{seconds}秒前"

        # 1 小时内：分钟
        minutes = seconds // 60
        if minutes < 60:
            return f"{minutes}分钟前"

        # 1 天内：小时
        hours = minutes // 60
        if hours < 24:
            return f"{hours}小时前"

        # 1 个月内：天（这里按30天计算）
        days = hours // 24
        if days < 30:
            return f"{days}天前"

        # 1 年内：月（按年月差计算，更准确）
        # 先计算年月总差值
        year_diff = now.year - past.year
        month_diff = now.month - past.month
        total_months = year_diff * 12 + month_diff
        if total_months < 12:
            return f"{total_months}月前"

        # 超过 1 年：年
        # 如果当前月-日还没到过 past 的月-日，则实足年数要减 1
        years = year_diff
        if (now.month, now.day) < (past.month, past.day):
            years -= 1
        return f"{years}年前"

    def _update_status_label(self):
        """更新状态标签"""
        valid_count = len(self.valid_configs)
        occupied_count = len(self.occupied_configs)
        invalid_count = len(self.invalid_configs)
        
        status_text = f"可用配置: {valid_count}个  占用配置: {occupied_count}个  无效配置: {invalid_count}个"
        self.status_label.config(text=status_text)

    def create_new_config(self):
        """创建新配置"""
        self.new_config_to_main_gui(first_run=False)

    def import_config(self):
        """导入配置文件"""
        server_utils.logger_print(msg="importing config file", custom_logger=logger)

        try:
            # 选择配置文件
            file_path = filedialog.askopenfilename(
                title="选择配置文件",
                filetypes=[("INI files", "*.ini")],
                initialdir=self.last_config_dir_on_import_config
            )
            
            if not file_path:
                return
            
            server_utils.logger_print(msg=f"selected config file: {file_path}", custom_logger=logger)
            can_register, fail_reason = self.config_manager.check_config_registrable(file_path)
            if not can_register:
                messagebox.showerror("错误", f"导入的配置文件不可使用: {fail_reason}")
                self.refresh_config_list()
                return

            server_utils.logger_print(msg=f"registered imported config: {file_path}", custom_logger=logger)
            self.last_config_dir_on_import_config=os.path.dirname(file_path)
            if messagebox.askyesno("加载到主界面","是否使用导入的配置进入主界面?"):
                # 注册配置
                self.config_manager.register_config(file_path)
                # 进入主界面
                self.launch_main_gui(file_path)
            else:
                self.config_manager.load_import_config(file_path)
                # 刷新配置列表
                self.refresh_config_list()
            
        except Exception as e:
            server_utils.logger_print(msg="error importing config!", custom_logger=logger, use_exception=True, exception=e, print_error=True)
            messagebox.showerror("错误", f"导入配置失败：{str(e)}")

    def clear_invalid_configs(self):
        """清理无效配置"""
        server_utils.logger_print(msg="clearing invalid configs", custom_logger=logger)
        try:
            had_deleted_count=self.config_manager.delete_invalid_configs()
            server_utils.logger_print(msg="invalid configs cleared successfully", custom_logger=logger)
            if had_deleted_count>0:
                messagebox.showinfo("成功", f"成功删除无效配置{had_deleted_count}个")
            else:
                messagebox.showinfo("提示", "无效配置不存在")
        except Exception as e:
            server_utils.logger_print(msg="error clearing invalid configs!", custom_logger=logger, use_exception=True, exception=e, print_error=True)
            messagebox.showerror("错误", f"清理无效配置失败：{str(e)}")
        finally:
            self.refresh_config_list()


    def select_config(self):
        """选择配置进入主界面"""
        selected = self.config_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个配置")
            return
        
        # 获取选中的配置信息
        item = self.config_tree.item(selected[0])
        values = item['values']
        
        if not values:
            return
        
        status = values[0]
        config_path = values[2]
        
        # 检查配置状态
        if "占用" in status:
            messagebox.showwarning("警告", "该配置正被其他进程占用，无法使用")
            return
        elif "无效" in status:
            messagebox.showwarning("警告", "该配置无效，无法使用")
            return
        
        server_utils.logger_print(msg=f"selecting config: {config_path}", custom_logger=logger)
        # 进入主界面
        self.launch_main_gui(config_path)

    def launch_main_gui(self, config_path: str):
        """启动主界面"""
        server_utils.logger_print(msg=f"launching main gui with config: {config_path}", custom_logger=logger)
        try:
            # 动态导入主界面类
            from webhook_server_gui import WebhookServerGUI
            # 启动主界面
            main_app = WebhookServerGUI(config_path)
        except Exception as e:
            server_utils.logger_print(msg="error importing main gui!", custom_logger=logger, use_exception=True, exception=e, print_error=True)
            messagebox.showerror("错误", f"导入主界面失败：{str(e)}")
            self.refresh_config_list()
            return
        try:
            # 关闭当前窗口:由于可能加载当前配置文件可能出现问题，在出现问题之后不能执行界面关闭操作
            self.on_closing()
            main_app.root.mainloop()
        except Exception as e:
            server_utils.logger_print(msg="error launching main gui!", custom_logger=logger, use_exception=True, exception=e, print_error=True)
            messagebox.showerror("错误", f"启动主界面失败：{str(e)}")
            sys.exit(1)

    def on_closing(self):
        """关闭事件处理"""
        server_utils.logger_print(msg="config selection gui closing", custom_logger=logger)
        if hasattr(self, "root"):
            gui_utils.gui_close(self.root)


if __name__ == "__main__":
    try:
        app = ConfigSelectionGUI()
        # 首次运行时 'root' 是不存在的
        if hasattr(app, "root"):
            app.root.mainloop()
    except Exception as unknown_exp:
        server_utils.logger_print(msg="unknown error occurred!", custom_logger=logger, use_exception=True, exception=unknown_exp, print_error=True)
        messagebox.showerror("程序崩溃", f"发生未预期错误: {type(unknown_exp).__name__}\n\n请将日志文件发送给开发者处理")
