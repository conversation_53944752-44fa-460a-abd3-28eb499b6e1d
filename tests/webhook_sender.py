import asyncio
import concurrent
import random
import re
import string

import aiohttp
import requests

import models.server_properties
from config import constants
from utils import server_utils


# 中文字符池：在常用汉字 Unicode 范围（0x4E00–0x9FFF）内随机挑选
def random_chinese_char():
    # 常用汉字大致在 0x4E00 到 0x9FFF 之间
    return chr(random.randint(0x4E00, 0x9FFF))


# 随机生成一个长度为 n 的字符串，包含英文字符（大小写字母和数字）和中文汉字
def random_cn_en_string(n: int) -> str:
    if n <= 0:
        raise ValueError("参数 n 必须为大于 0 的整数")

    # 英文字符池：包含大小写字母和数字
    en_pool = string.ascii_letters + string.digits

    result_chars = []
    for _ in range(n):
        # 随机决定是取英文字符还是中文字符，比例可自行调整
        if random.random() < 0.5:
            # 取英文字符
            result_chars.append(random.choice(en_pool))
        else:
            # 取一个随机中文汉字
            result_chars.append(random_chinese_char())

    return ''.join(result_chars)


# 给client_id_set中每一个元素重复生成 random_cn_en_string 函数生成长度在5-10之间的随机字符串 message_count 次,拼接"client_id,当前生成的字符串"并将结果写入到 out_path 文件中
def generate_stress_test_data(client_id_set: set, message_count: int, out_path: str):
    if not client_id_set:
        raise ValueError("client_id_set 不能为空")
    if not isinstance(message_count, int) or message_count <= 0:
        raise ValueError("message_count 必须是大于 0 的整数")
    if not isinstance(out_path, str) or not out_path:
        raise ValueError("out_path 不能为空字符串")

    # 确保目录存在
    out_path = server_utils.get_real_path_create_dir(out_path)

    # 写入文件（覆盖模式）
    with open(out_path, 'w', encoding='utf-8') as f:
        # 首行
        f.write("client_id,message\n")
        # 逐条写入
        for client_id in client_id_set:
            for _ in range(message_count):
                length = random.randint(5, 10)
                msg = random_cn_en_string(length)
                f.write(f"{client_id},{msg}\n")


# 根据客户端id文件路径获取所以的客户端id,然后使用generate_stress_test_data函数生成测试数据
def generate_stress_test_data_by_client_id_file(client_keys: set, message_count: int, out_path: str):
    if not isinstance(out_path, str) or not out_path:
        raise ValueError("out_path 不能为空字符串")

    client_id_set = set()
    # 读取客户端id文件
    for client_key in client_keys:
        if not re.match(r'^[a-zA-Z0-9]{10,14}$', client_key):
            raise ValueError(f"line {client_key}: invalid key format")
        if client_key in client_id_set:
            raise ValueError(f"line {client_key}: duplicate key")
        client_id_set.add(client_key)


    # 生成测试数据
    generate_stress_test_data(client_id_set, message_count, out_path)


send_data_api_url = 'http://localhost:8000/webhook/save'


def send_by_client_id_request(client_id: str, message: str):
    headers = {'x-client-key': f'{client_id}'}
    data = f'{{"content":"{message}"}}'
    response = requests.post(send_data_api_url, headers=headers, data=data)
    response_json = response.json()
    if response_json['status'] != 'success':
        print(f"response: {response_json}")


async def async_send_request(client_id: str, message: str):
    async with aiohttp.ClientSession() as session:
        headers = {
            'x-client-key': client_id,
            'Content-Type': 'application/json'  # 必须声明
        }
        data = f'{{"content":"{message}"}}'
        async with session.post(send_data_api_url, headers=headers, data=data) as response:
            response_json = await response.json()
            if response_json['status'] != 'success':
                print(f"response: {response_json}")


def async_send_by_client_id_file(client_keys: set, message_count: int, out_path: str):
    # 生成测试数据
    generate_stress_test_data_by_client_id_file(client_keys, message_count, out_path)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        with open(out_path, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i == 0:
                    continue
                client_id, message = line.strip().split(',')
                loop.run_until_complete(async_send_request(client_id, message))
    finally:
        loop.close()


def thread_send_by_client_id_file(client_keys: set, message_count: int, out_path: str):
    # 生成测试数据
    generate_stress_test_data_by_client_id_file(client_keys, message_count, out_path)
    data_list = []
    with open(out_path, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if i == 0:
                continue
            client_id, message = line.strip().split(',')
            data_list.append((client_id, message))

    with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
        [executor.submit(send_by_client_id_request, client_id, message) for client_id, message in data_list]

if __name__ == '__main__':
    # 测试 生成的数据用于apipost压力测试
    config_path="C:\\Users\\<USER>\\.webhook_server\\server_config_1.ini"
    client_keys = models.server_properties.ServerProperties(config_path, constants.DEFAULT_SCHEDULER_CONFIG).client_info_properties.keys()

    message_count = 100
    out_path = 'D:/Temp/stress_test_data.txt'
    # generate_stress_test_data_by_client_id_file(client_keys, message_count, out_path)
    # send_by_client_id_request('xascxadsas5c', '测试消息-1')
    # async_send_by_client_id_file(client_keys, message_count, out_path)
    thread_send_by_client_id_file(client_keys, message_count, out_path)
#
