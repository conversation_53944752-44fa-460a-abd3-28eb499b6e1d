## 1. 运行测试

**目的**：验证在各种运行场景下，配置文件加载、日志输出及进程占用情况。

### 1.1 加载错误配置文件

* 使用以下三种方式分别运行，加载损坏或无效的配置文件，观察日志中是否正确报错：

    1. 命令行方式运行 `config_selection_gui`
    2. 命令行方式运行 `webhook_server_gui`
    3. 命令行方式运行 `webhook_server`

### 1.2 并发加载同一配置文件

* 同时启动多个实例，全部加载同一个有效配置文件，检查并发访问锁定及日志提示：

    * 两个 `webhook_server_gui`
    * 两个 `webhook_server`
    * `webhook_server_gui` + `webhook_server`

```shell
python D:\Git\python-samples-hub\src\config_selection_gui.py
python D:\Git\python-samples-hub\src\webhook_server_gui.py --config C:\Users\<USER>\.webhook_server\server_config_4.ini
python D:\Git\python-samples-hub\src\webhook_server_command.py --config C:\Users\<USER>\.webhook_server\server_config_4.ini
```

### 1.3 并发加载不同配置文件

* 重复 **1.2** 的并发启动场景，但每个实例加载不同的配置文件，确认互不干扰。

## 2. 使用测试

**目的**：验证各组件功能的正确性与资源清理。

### 2.1 单实例使用测试

#### 2.1.1 webhook\_server

1. 加载有效配置文件并启动服务。
2. 调用所有 API，验证响应正确。
3. 使用压测工具加载代码生成数据进行发信压测
4. 关闭进程，确认进程占用被释放。

#### 2.1.2 webhook\_server\_gui

1. 加载有效配置文件并启动 GUI。
2. 浏览各界面项，确认显示内容正确。
3. 修改配置并保存，重启后验证变更生效。
4. 修改不保存，重启后确认保持原配置。
5. 在 GUI 中启动 Server，测试 API 可用性。
6. 关闭 Server，确认 GUI 不再显示数据且进程结束。

#### 2.1.3 config\_selection\_gui

1. 启动进入配置列表界面。
2. 新建配置，退出后重启，确认列表中出现新建配置。
3. 选择新配置，进入主界面，并启动 Server，验证 API 正常。
4. 导入：

    * 完整配置文件。
    * 缺少自定义项的配置文件。
5. 在数据库后台将某项配置标记失效，刷新 GUI，确认出现失效警告并自动更新列表。
6. 命令行启动列表中某有效配置，再在 GUI 中选择该配置，确认“配置已被占用”提示。
7. 关闭命令行实例，刷新 GUI，确认该配置显示可用。
8. 在列表中清除失效配置，确认界面与数据库中均移除该记录。
9. 双击选择一个有效配置，进入主界面，执行 **2.1.2** 中 `webhook_server_gui` 的测试步骤。

### 2.2 多实例并发使用测试

* 根据 **1.3** 的并发启动组合，分别运行多个实例。
* 对每个实例执行 **2.1** 的测试，验证互不干扰与稳定性。

---

*测试期间请开启详细日志，确保记录所有关键步骤与异常信息。*
