#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试tooltip功能的简单脚本
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from utils import gui_utils
    print("成功导入gui_utils模块")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("Tooltip测试")
    root.geometry("600x400")
    
    # 创建测试TreeView
    columns = ("status", "name", "last_used", "info")
    tree = ttk.Treeview(root, columns=columns, show="headings", selectmode="browse")
    
    # 设置列属性
    tree.column("status", width=80, anchor="center")
    tree.column("name", width=200, anchor="w")
    tree.column("last_used", width=120, anchor="center")
    tree.column("info", width=150, anchor="w")
    
    # 设置表头
    tree.heading("status", text="状态")
    tree.heading("name", text="配置名称")
    tree.heading("last_used", text="最后使用")
    tree.heading("info", text="附加信息")
    
    tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    # 创建tooltip
    tooltip = gui_utils.create_treeview_row_tooltip(tree)
    
    # 添加测试数据
    tree.insert("", "end", iid="1", values=("🟢 可用", "测试配置1", "1小时前", ""))
    tree.insert("", "end", iid="2", values=("🔴 占用", "测试配置2", "2小时前", "PID: 1234"))
    
    # 设置tooltip
    tooltip.set_row_tooltip("1", "配置路径: D:/test/config1.ini")
    tooltip.set_row_tooltip("2", "配置路径: D:/test/config2.ini")
    
    print("测试窗口创建成功，请将鼠标悬停在行上测试tooltip功能")
    root.mainloop()
    
except ImportError as e:
    print(f"导入模块失败: {e}")
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
